<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * DMS Service để kiểm tra trạng thái cancel booking từ hệ thống DMS
 */
class DmsService
{
    /**
     * DMS API base URL
     */
    protected $dmsApiUrl;

    /**
     * DMS API credentials
     */
    protected $dmsApiKey;

    /**
     * Cache timeout cho column check (5 phút)
     */
    const COLUMN_CHECK_CACHE_TIMEOUT = 300;

    public function __construct()
    {
        $this->dmsApiUrl = config('services.dms.api_url', env('DMS_API_URL'));
        $this->dmsApiKey = config('services.dms.api_key', env('DMS_API_KEY'));
    }

    /**
     * Kiểm tra xem column rr_cancel có tồn tại trong DMS table không
     * Sử dụng cache để tránh call API nhiều lần
     *
     * @param string $table Table name
     * @return bool
     */
    public function hasRrCancelColumn($table = 'bookings')
    {
        $cacheKey = "dms_column_check_{$table}_rr_cancel";
        
        return Cache::remember($cacheKey, self::COLUMN_CHECK_CACHE_TIMEOUT, function () use ($table) {
            try {
                $response = Http::timeout(10)
                    ->withHeaders([
                        'Authorization' => 'Bearer ' . $this->dmsApiKey,
                        'Accept' => 'application/json',
                    ])
                    ->get($this->dmsApiUrl . "/api/table/{$table}/columns");

                if ($response->successful()) {
                    $columns = $response->json('data', []);
                    return in_array('rr_cancel', $columns);
                }

                Log::warning('DMS API failed to check columns', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                
                return false;

            } catch (\Exception $e) {
                Log::error('Error checking DMS column existence: ' . $e->getMessage());
                return false;
            }
        });
    }

    /**
     * Kiểm tra xem booking có bị cancel không dựa trên DMS data
     *
     * @param mixed $bookingId ID của booking cần kiểm tra
     * @param string $table Table name
     * @return bool true nếu booking bị cancel, false nếu không
     */
    public function isBookingCancelled($bookingId, $table = 'bookings')
    {
        try {
            // Nếu column không tồn tại, return false (không cancel)
            if (!$this->hasRrCancelColumn($table)) {
                return false;
            }

            $response = Http::timeout(10)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $this->dmsApiKey,
                    'Accept' => 'application/json',
                ])
                ->get($this->dmsApiUrl . "/api/{$table}/{$bookingId}");

            if ($response->successful()) {
                $booking = $response->json('data');
                $rrCancel = $booking['rr_cancel'] ?? '';
                
                // Kiểm tra xem có chứa text "キャンセル" không
                return !empty($rrCancel) && strpos($rrCancel, 'キャンセル') !== false;
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Error checking booking cancel status from DMS: ' . $e->getMessage());
            // Trong trường hợp lỗi, return false để không ảnh hưởng đến logic chính
            return false;
        }
    }

    /**
     * Lấy danh sách các booking ID bị cancel từ DMS
     *
     * @param array $bookingIds Danh sách booking IDs cần kiểm tra
     * @param string $table Table name
     * @return array Danh sách booking IDs bị cancel
     */
    public function getCancelledBookingIds(array $bookingIds, $table = 'bookings')
    {
        try {
            // Nếu column không tồn tại, return empty array
            if (!$this->hasRrCancelColumn($table)) {
                return [];
            }

            if (empty($bookingIds)) {
                return [];
            }

            $response = Http::timeout(15)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $this->dmsApiKey,
                    'Accept' => 'application/json',
                ])
                ->post($this->dmsApiUrl . "/api/{$table}/check-cancelled", [
                    'booking_ids' => $bookingIds,
                    'cancel_text' => 'キャンセル'
                ]);

            if ($response->successful()) {
                return $response->json('data.cancelled_ids', []);
            }

            Log::warning('DMS API failed to get cancelled booking IDs', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [];

        } catch (\Exception $e) {
            Log::error('Error getting cancelled booking IDs from DMS: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Lọc bỏ các booking bị cancel từ danh sách
     *
     * @param array $bookings Danh sách booking data
     * @param string $idField Tên field chứa booking ID (default: 'id')
     * @param string $table Table name
     * @return array Danh sách booking sau khi lọc bỏ cancelled bookings
     */
    public function filterCancelledBookings(array $bookings, $idField = 'id', $table = 'bookings')
    {
        if (empty($bookings)) {
            return $bookings;
        }

        try {
            // Lấy danh sách booking IDs
            $bookingIds = array_column($bookings, $idField);
            
            // Lấy danh sách booking IDs bị cancel
            $cancelledIds = $this->getCancelledBookingIds($bookingIds, $table);

            if (empty($cancelledIds)) {
                return $bookings;
            }

            // Lọc bỏ các booking bị cancel
            return array_filter($bookings, function($booking) use ($cancelledIds, $idField) {
                $bookingId = is_array($booking) ? $booking[$idField] : $booking->{$idField};
                return !in_array($bookingId, $cancelledIds);
            });

        } catch (\Exception $e) {
            Log::error('Error filtering cancelled bookings: ' . $e->getMessage());
            // Trong trường hợp lỗi, return danh sách gốc
            return $bookings;
        }
    }

    /**
     * Kiểm tra DMS service có available không
     *
     * @return bool
     */
    public function isDmsAvailable()
    {
        try {
            $response = Http::timeout(5)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $this->dmsApiKey,
                    'Accept' => 'application/json',
                ])
                ->get($this->dmsApiUrl . '/api/health');

            return $response->successful();

        } catch (\Exception $e) {
            Log::error('DMS service not available: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Clear cache cho column check
     *
     * @param string $table
     * @return void
     */
    public function clearColumnCache($table = 'bookings')
    {
        $cacheKey = "dms_column_check_{$table}_rr_cancel";
        Cache::forget($cacheKey);
    }
}
