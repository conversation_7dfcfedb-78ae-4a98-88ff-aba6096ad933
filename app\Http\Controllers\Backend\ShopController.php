<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Base\BackendController;
use App\Repositories\ShopInfoRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

/**
 * Class ShopController
 * @package App\Http\Controllers\Backend
 */
class ShopController extends BackendController
{
    public function __construct(ShopInfoRepository $shopInfoRepository)
    {
        parent::__construct();
        $this->setRepository($shopInfoRepository);
        $this->setTitle(getConfig('page_title.backend.shop.index'));
        $this->setBackUrlDefault('shop.index');
    }

    public function prepareValid()
    {
        $params = $this->trimSpaceParams();

        if (!empty($params['publish_from_dt_date']) && !empty($params['publish_from_dt_time'])) {
            $publishFrom = data_get($params, 'publish_from_dt_date') . ' ' . data_get($params, 'publish_from_dt_time');
            $params['publish_from_dt'] = Carbon::parse($publishFrom)->format('Y-m-d H:i:s');
        } else {
            $params['publish_from_dt'] = null;
        }

        if (!empty($params['publish_to_dt_date']) && !empty($params['publish_to_dt_time'])) {
            $publishTo = data_get($params, 'publish_to_dt_date') . ' ' . data_get($params, 'publish_to_dt_time');
            $params['publish_to_dt'] = Carbon::parse($publishTo)->format('Y-m-d H:i:s');
        }
        else {
            $params['publish_to_dt'] = null;
        }

        foreach (getConfig('week_day') as $day) {
            $params[$day . '_closing_flg'] = empty($params[$day . '_closing_flg']) ? getConstant('CLOSING_FLG.OFF') : getConstant('CLOSING_FLG.ON');

            if (!empty($params[$day . '_closing_flg']) && $params[$day . '_closing_flg'] == getConstant('CLOSING_FLG.ON')) {
                $params[$day . '_operation_from'] = getConstant('DEFAULT_HOUR');
                $params[$day . '_operation_to'] = getConstant('DEFAULT_HOUR');
            }
        }

        $params['early_reservable_tel_flg'] = empty($params['early_reservable_tel_flg']) ? getConstant('CLOSING_FLG.OFF') : getConstant('CLOSING_FLG.ON');

        if (!empty($params['id'])) {
            //get old values
            $oldEntity = $this->_findOrNewEntity($params['id'], true)->toArray();
            $params['shop_user_pwd'] = empty($params['shop_user_pwd']) ? $oldEntity['shop_user_pwd'] : $params['shop_user_pwd'];
            $params['publish_from_dt_old'] = $oldEntity['publish_from_dt'];
        }

        return $params;
    }

    public function store()
    {
        if ($this->_emptyFormData()) {
            return $this->_backToStart()->withErrors(trans('messages.create_failed'));
        }

        $params = $this->_getFormData(false);

        if (!$this->getRepository()->getValidator()->validateCreate($params)) {
            return $this->_backToStart()->withErrors($this->getRepository()->getValidator()->errors());
        }

        DB::beginTransaction();
        try {
            $entity = $this->_findEntityForStore();
            $this->fireEvent('before_store', $entity);
            $this->_moveFileFromTmpToMedia($entity);

            //save value
            $entity->shop_key = sha1($entity->shop_user_email) . date('YmdH');
            $entity->ins_id = $this->getCurrentUser()->id;

            $entity->save();
            $this->_saveRelations($entity);
            // add new
            $this->fireEvent('before_store_commit', $entity);
            DB::commit();

            $this->fireEvent('after_store_commit', $entity);
            $this->fireEvent('after_store', $entity);
            return $this->_backToStart()->withSuccess(trans('messages.create_success'));
        } catch (\Exception $e) {
            logError($e);
            $this->_removeMediaFile(isset($entity) ? $entity : null);
            DB::rollBack();
        }
        return $this->_backToStart()->withErrors(trans('messages.create_failed'));
    }

    public function update($id)
    {
        $id = $this->_buildParamByKey($id);
        if ($this->_emptyFormData()) {
            return $this->_backToStart()->withErrors(trans('messages.update_failed'));
        }
        $params = $this->_getFormData(false);
        if (!$this->getRepository()->getValidator()->validateUpdate($params)) {
            return $this->_backToStart()->withErrors($this->getRepository()->getValidator()->errors());
        }

        DB::beginTransaction();
        try {
            $entity = $this->_findEntityForUpdate($id);
            $this->fireEvent('before_update', $entity);
            $this->_moveFileFromTmpToMedia($entity);

            //save value
            if (backendGuard()->check()) {
                $entity->upd_id = $this->getCurrentUser()->id;
            } elseif (shopGuard()->check()) {
                $entity->upd_id = shopGuard()->user()->id;
            }

            $entity->save();
            // fire after save
            // fire before save relation
            $this->_saveRelations($entity);
            // fire after save relation
            $this->fireEvent('before_update_commit', $entity);
            DB::commit();
            $this->fireEvent('after_update_commit', $entity);
            $this->fireEvent('after_update', $entity);
            if (backendGuard()->check()) {
                return $this->_backToStart()->withSuccess(trans('messages.update_success'));
            } elseif (shopGuard()->check()) {
                return redirect()->to(buildShopInfoUrl())->withSuccess(trans('messages.update_success'));
            }
        } catch (\Exception $e) {
            logError($e);
            $this->_removeMediaFile(isset($entity) ? $entity : null);
            DB::rollBack();
        }
        if (backendGuard()->check()) {
            return $this->_backToStart()->withErrors(trans('messages.update_failed'));
        } elseif (shopGuard()->check()) {
            return redirect()->to(buildShopInfoUrl())->withErrors(trans('messages.update_failed'));
        }
    }

    protected function _prepareEdit($id = null)
    {
        $entity = $this->_findOrNewEntity($id, true);
        $this->getPublishDTForView($entity);

        return parent::_prepareEdit($id);
    }

    protected function _prepareCreate()
    {
        $entity = $this->_findEntityForStore();
        $this->getPublishDTForView($entity);

        return parent::_prepareCreate(); // TODO: Change the autogenerated stub
    }

    public function getDateTimePublish($publishDt)
    {
        $date = $time = '';

        if (isset($publishDt)) {
            $date = Carbon::parse($publishDt)->format('Y/m/d');
            $time = Carbon::parse($publishDt)->format('H:i');
        }

        return [$date, $time];
    }

    public function getPublishDTForView($entity)
    {
        list($publishFromDate, $publishFromTime) = $this->getDateTimePublish($entity->publish_from_dt);
        list($publishToDate, $publishToTime) = $this->getDateTimePublish($entity->publish_to_dt);

        $this->setViewData([
            'publishFromDate' => $publishFromDate,
            'publishFromTime' => $publishFromTime,
            'publishToDate' => $publishToDate,
            'publishToTime' => $publishToTime,
        ]);
    }

    public function changePass($id)
    {
        $params = request()->all();

        //case edit
        if (!empty($id)) {
            //get old values
            $oldEntity = $this->_findOrNewEntity($id, true)->toArray();
            // if change password
            if (!empty($params['shop_user_pwd_current'])) {
                $params['check_current_password'] = $oldEntity['shop_user_pwd'];
                if (!Hash::check($params['shop_user_pwd_current'], $params['check_current_password'])) {
                    $params['check_current_password'] = '';
                } else {
                    $params['check_current_password'] = true;
                }
            }
        }

        if (!$this->getRepository()->getValidator()->validateChangePass($params)) {
            $params['show_flash'] = 'disabled';
            $errors = null;
            $backStart = false;
            $errors = $errors ? $errors : $this->getRepository()->getValidator()->errorsBag()->getMessages();
            $result = ($backStart) ? $this->_backToStart()->withErrors($errors) : $this->_back()->withErrors($errors)// send back all errors
            ->withInput($params); // send back the input
            return $result;
        }

        DB::beginTransaction();
        try {
            $entity = $this->_findEntityForUpdate($id);
            //save value
            if (backendGuard()->check()) {
                $entity->ins_id = $this->getCurrentUser()->id;
            } elseif (shopGuard()->check()) {
                $entity->ins_id = shopGuard()->user()->id;
            }
            $entity->shop_user_pwd = $params['shop_user_pwd'];
            $entity->save();
            DB::commit();
            if (backendGuard()->check()) {
                return $this->_backToStart()->withSuccess(trans('messages.update_success'));
            } elseif (shopGuard()->check()) {
                return redirect()->to(buildShopInfoUrl())->withSuccess(trans('messages.update_success'));
            }
        } catch (\Exception $e) {
            logError($e);
            DB::rollBack();
        }
        if (backendGuard()->check()) {
            return $this->_backToStart()->withErrors(trans('messages.update_failed'));
        } elseif (shopGuard()->check()) {
            return redirect()->to(buildShopInfoUrl())->withErrors(trans('messages.update_failed'));
        }
    }
}
