<?php
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "backend" middleware group. Now create something great!
|
*/
Route::get('/', function (){
})->middleware('redirectDefault');

Route::post('pl-import/import', [
    'as' => 'pl.import.import',
    'uses' => 'PlImportController@import'
]);

Route::get('table-columns/{table}', [
    'as' => 'pl.import.table.columns',
    'uses' => 'PlImportController@getTableColumns'
]);
Route::resource('pl-import', 'PlImportController');

// Shop
Route::resource('shop', 'ShopController');

// Calender setting
Route::get('calendar/setting/get-reserve-denials-by-shop/{shop_id}', 'CalendarController@getReserveDenialsByShop')->name('calendar.getReserveDenialsByShop');
Route::post('calendar/setting/save-reserve-denials', 'CalendarController@saveReserveDenials')->name('calendar.saveReserveDenials');
Route::get('calendar/setting/get-data-calendar', 'CalendarController@getDataCalendar')->name('calendar.getDataCalendar');
Route::post('calendar/setting/create-or-update-flg-tel', 'CalendarController@createOrUpdateFlgTel')->name('calendar.CreateOrUpdateFlgTel');
Route::get('calendar/setting/get-reserve-operation-time', 'CalendarController@getOperationTime')->name('calendar.getOperationTime');
Route::post('calendar/setting/save-operation-time', 'CalendarController@saveOperationTime')->name('calendar.saveOperationTime');
Route::get('calendar/setting/{shop_id}', 'CalendarController@setting')->name('calendar.setting');

//change password
Route::post('shop/{shop}/edit', 'ShopController@changePass')->name('shop.change_pass');

authRoutes('backend');

